# 算力市场部署功能完善

## 功能概述

本次更新完善了算力市场的应用部署功能，支持用户选择应用、配置环境变量，并执行真实的部署操作。

## 主要功能

### 1. 环境变量配置组件 (`EnvironmentVariablesInput.jsx`)

- **功能**: 独立的环境变量配置组件，支持动态添加/删除键值对
- **特性**:
  - 支持动态添加和删除环境变量
  - 变量名格式验证（大写字母、数字、下划线）
  - 响应式布局
  - 可配置的标题和大小
  - 禁用状态支持

### 2. 部署服务接口 (`deployService.js`)

- **主要接口**: `POST /ihmp/app/deploy`
- **请求参数**:
  ```javascript
  {
    "machineId": "1",        // 机器ID
    "appId": 3,              // 应用模板ID
    "appName": "xxxxx-9999", // 应用名[a-z0-9]
    "cpuRequest": 1,         // CPU核数
    "memoryRequest": 1,      // 内存大小 单位G
    "gpuRequest": 1,         // GPU卡数
    "envVars": {"key":"value"} // 环境变量
  }
  ```

- **其他功能**:
  - 部署状态查询
  - 部署日志获取
  - 应用启停控制
  - 配置更新
  - 监控数据获取

### 3. 订购向导增强 (`InstanceOrderWizard.jsx`)

#### 新增功能:
- **应用配置步骤**: 当用户选择应用时，增加专门的配置步骤
- **环境变量配置**: 
  - 支持用户自定义环境变量
  - 显示应用模板预设的环境变量
  - 允许用户修改预设环境变量的值
- **应用实例命名**: 支持符合规范的应用实例名称（小写字母、数字、连字符）
- **真实部署调用**: 集成部署服务接口，执行真实的部署操作

#### 工作流程:
1. **确认配置**: 选择机器、应用、资源配置
2. **应用配置** (如果选择了应用): 配置应用参数和环境变量
3. **订单确认**: 确认所有配置并执行部署

### 4. 算力市场页面优化 (`ComputeMarket.jsx`)

- **部署状态支持**: 实例创建时支持"部署中"状态
- **部署信息保存**: 保存部署ID、环境变量等信息
- **状态反馈**: 根据是否有部署操作显示不同的成功消息

## 技术实现

### 环境变量处理
```javascript
// 合并环境变量：预设 + 用户自定义
const envVars = {};

// 添加预设环境变量
if (appConfigValues.presetEnvVars) {
  Object.assign(envVars, appConfigValues.presetEnvVars);
}

// 添加用户自定义环境变量
if (appConfigValues.environmentVariables) {
  appConfigValues.environmentVariables.forEach(envVar => {
    if (envVar.key && envVar.value) {
      envVars[envVar.key] = envVar.value;
    }
  });
}
```

### 部署请求构建
```javascript
const deployData = {
  machineId: device.id.toString(),
  appId: parseInt(application.id),
  appName: appConfigValues.appName,
  cpuRequest: orderConfig.gpuCount,
  memoryRequest: parseInt(device.memorySize),
  gpuRequest: orderConfig.gpuCount,
  envVars: envVars
};
```

## 用户体验改进

1. **智能默认值**: 应用实例名称自动生成，符合命名规范
2. **预设变量展示**: 清晰展示应用模板的预设环境变量
3. **实时反馈**: 部署过程中的状态提示和错误处理
4. **响应式设计**: 适配不同屏幕尺寸

## 部署状态管理

- `PENDING`: 等待中
- `DEPLOYING`: 部署中  
- `RUNNING`: 运行中
- `STOPPED`: 已停止
- `FAILED`: 部署失败
- `CANCELLED`: 已取消

## 下一步计划

1. **部署状态轮询**: 实时更新部署进度
2. **部署日志查看**: 支持查看详细的部署日志
3. **配置模板**: 支持保存和复用环境变量配置
4. **批量部署**: 支持一次部署多个应用实例
5. **回滚功能**: 支持部署失败时的快速回滚

## 使用说明

1. 进入算力市场页面
2. 选择合适的计算资源
3. 点击"立即租用"
4. 在订购向导中选择应用（可选）
5. 如果选择了应用，配置环境变量和应用参数
6. 确认订单并执行部署
7. 跳转到"我的实例"页面查看部署状态
