import React, { useState, useMemo } from 'react';
import {
  Modal,
  Steps,
  Card,
  Button,
  Form,
  Input,
  Select,
  Switch,
  Divider,
  Alert,
  Slider,
  Row,
  Col,
  Typography,
} from 'antd';
import {
  Server,
  Package,
  Settings,
  DollarSign,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
} from 'lucide-react';
import { formatPrice } from '@/utils/format';
import EnvironmentVariablesInput from './EnvironmentVariablesInput';
import { deployService } from '@/services/deployService';
import { toast } from 'sonner';

const { Option } = Select;
const { Text } = Typography;

const InstanceOrderWizard = ({
  visible,
  onCancel,
  onConfirm,
  device,
  application = null,
  title = '订购实例',
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 订购配置状态
  const [orderConfig, setOrderConfig] = useState({
    instanceName: '',
    pricingModel: 'ON_DEMAND', // 计费模式：只支持按需计费
    duration: 1, // 时长（小时）
    gpuCount: 1, // GPU 卡数
    extraDiskSize: 0, // 额外磁盘大小（GB，步进10G）
    autoInstallApp: true,
    autoRenew: false,
    appConfig: {},
  });

  // 动态生成步骤
  const hasAppConfig = application && orderConfig.autoInstallApp;

  console.log('Debug info:', {
    application: !!application,
    applicationName: application?.name,
    autoInstallApp: orderConfig.autoInstallApp,
    hasAppConfig,
    currentStep,
  });

  // 根据是否有应用配置来确定步骤
  const steps = useMemo(() => {
    const baseSteps = [
      {
        title: '确认配置',
        description: '确认设备和应用信息',
        icon: <Server className="w-4 h-4" />,
      },
    ];

    if (hasAppConfig) {
      baseSteps.push({
        title: '应用配置',
        description: '配置应用参数',
        icon: <Settings className="w-4 h-4" />,
      });
    }

    baseSteps.push({
      title: '订单确认',
      description: '确认订单并支付',
      icon: <CheckCircle className="w-4 h-4" />,
    });

    return baseSteps;
  }, [hasAppConfig]);

  // 同步表单字段值与状态
  React.useEffect(() => {
    form.setFieldsValue({
      autoInstallApp: orderConfig.autoInstallApp,
      autoRenew: orderConfig.autoRenew,
      instanceName: orderConfig.instanceName,
      duration: orderConfig.duration,
    });
  }, [
    form,
    orderConfig.autoInstallApp,
    orderConfig.autoRenew,
    orderConfig.instanceName,
    orderConfig.duration,
  ]);

  // 当应用配置状态变化时，重置步骤（如果当前不在第一步）
  React.useEffect(() => {
    // 如果当前在第二步或第三步，且应用配置状态发生变化，重置到第一步
    if (currentStep > 0) {
      const expectedStepCount = hasAppConfig ? 3 : 2;
      if (steps.length !== expectedStepCount) {
        setCurrentStep(0);
      }
    }
  }, [hasAppConfig, currentStep, steps.length]);

  // 使用 useMemo 确保价格在 orderConfig 变化时重新计算
  const priceInfo = useMemo(() => {
    console.log('Recalculating price info due to dependency change');
    console.log('Current orderConfig:', orderConfig);
    console.log('Full device object:', device);
    console.log('Device resourcePricingList:', device?.resourcePricingList);
    console.log('Device pricePerHour:', device?.pricePerHour);

    // 检查是否有详细的价格列表（来自设备管理页面）
    if (device?.resourcePricingList && device.resourcePricingList.length > 0) {
      console.log('Using detailed pricing data from resourcePricingList');

      // 获取 GPU 价格（按小时计费）
      const gpuPricing = device.resourcePricingList.find(
        p => p.resourceType === 'GPU'
      );
      console.log('GPU pricing found:', gpuPricing);
      const gpuUnitPrice = gpuPricing?.pricePerUnit || 0;
      const gpuHourlyPrice = gpuUnitPrice * orderConfig.gpuCount;

      // 获取磁盘价格（按小时计费）
      const diskPricing = device.resourcePricingList.find(
        p => p.resourceType === 'DISK'
      );
      console.log('Disk pricing found:', diskPricing);
      const diskUnitPrice = diskPricing?.pricePerUnit || 0;
      const diskHourlyPrice = (orderConfig.extraDiskSize / 10) * diskUnitPrice; // 按10G计费

      // 应用价格
      const appPrice = application?.pricePerHour || 0;
      const appCost = orderConfig.autoInstallApp ? appPrice : 0;

      // 总价计算（统一按小时计费）
      const hourlyTotal = gpuHourlyPrice + diskHourlyPrice + appCost;
      const totalCost = hourlyTotal * orderConfig.duration;

      const result = {
        gpuPrice: gpuHourlyPrice,
        diskPrice: diskHourlyPrice,
        appPrice,
        appCost,
        totalCost,
        gpuUnitPrice,
        diskUnitPrice,
        hourlyTotal,
      };

      console.log('Price calculation result (detailed):', result);
      return result;
    }

    // 使用简单的价格数据（来自市场页面）
    if (device?.pricePerHour) {
      console.log('Using simple pricing data from pricePerHour');

      // 假设 pricePerHour 是整机价格，按GPU数量分摊
      const gpuUnitPrice = device.pricePerHour / (device.gpuNum || 1);
      const gpuHourlyPrice = gpuUnitPrice * orderConfig.gpuCount;

      // 磁盘价格估算（假设每10GB 0.01元/小时）
      const diskUnitPrice = 0.01;
      const diskHourlyPrice = (orderConfig.extraDiskSize / 10) * diskUnitPrice;

      // 应用价格
      const appPrice = application?.pricePerHour || 0;
      const appCost = orderConfig.autoInstallApp ? appPrice : 0;

      // 总价计算
      const hourlyTotal = gpuHourlyPrice + diskHourlyPrice + appCost;
      const totalCost = hourlyTotal * orderConfig.duration;

      const result = {
        gpuPrice: gpuHourlyPrice,
        diskPrice: diskHourlyPrice,
        appPrice,
        appCost,
        totalCost,
        gpuUnitPrice,
        diskUnitPrice,
        hourlyTotal,
      };

      console.log('Price calculation result (simple):', result);
      return result;
    }

    // 没有价格数据
    console.log('No pricing data available');
    return {
      gpuPrice: 0,
      diskPrice: 0,
      appCost: 0,
      appPrice: 0,
      totalCost: 0,
      gpuUnitPrice: 0,
      diskUnitPrice: 0,
      hourlyTotal: 0,
    };
  }, [device, orderConfig, application]);

  const handleNext = async () => {
    if (currentStep === 0) {
      try {
        // 根据是否有应用来决定验证哪些字段
        const fieldsToValidate = ['instanceName', 'duration', 'autoRenew'];
        if (application) {
          fieldsToValidate.push('autoInstallApp');
        }
        const values = await form.validateFields(fieldsToValidate);
        console.log('Form values from step 1:', values);
        setOrderConfig(prev => {
          const newConfig = { ...prev, ...values };
          console.log('Updated orderConfig:', newConfig);
          return newConfig;
        });

        // 进入下一步
        setCurrentStep(1);
      } catch (error) {
        console.error('表单验证失败:', error);
      }
    } else if (currentStep === 1 && hasAppConfig) {
      // 如果当前是步骤1且有应用配置，验证应用配置并进入最终确认步骤
      try {
        const appConfigValues = await form.validateFields();
        setOrderConfig(prev => ({
          ...prev,
          appConfig: appConfigValues,
        }));
        setCurrentStep(2);
      } catch (error) {
        console.error('应用配置验证失败:', error);
      }
    }
  };

  const handlePrev = () => {
    // 总是回到上一步
    setCurrentStep(currentStep - 1);
  };

  const handleConfirm = async () => {
    setLoading(true);
    try {
      // 如果选择了应用且自动安装，则调用部署接口
      if (orderConfig.autoInstallApp && application) {
        // 获取表单中的应用配置
        const appConfigValues = form.getFieldsValue();

        // 合并环境变量：预设环境变量 + 用户自定义环境变量
        const envVars = {};

        // 添加预设环境变量
        if (appConfigValues.presetEnvVars) {
          Object.assign(envVars, appConfigValues.presetEnvVars);
        }

        // 添加用户自定义环境变量
        if (appConfigValues.environmentVariables) {
          appConfigValues.environmentVariables.forEach(envVar => {
            if (envVar.key && envVar.value) {
              envVars[envVar.key] = envVar.value;
            }
          });
        }

        // 构建部署请求数据
        const deployData = {
          machineId: device.id.toString(),
          appId: parseInt(application.id),
          appName:
            appConfigValues.appName ||
            `${application.name
              .toLowerCase()
              .replace(/[^a-z0-9]/g, '-')}-${Date.now().toString().slice(-4)}`,
          cpuRequest: orderConfig.gpuCount, // 使用选择的GPU卡数作为CPU核数
          memoryRequest: parseInt(
            device.memorySize || device.memory?.match(/\d+/)?.[0] || '4'
          ), // 使用设备内存
          gpuRequest: orderConfig.gpuCount,
          envVars: envVars,
        };

        console.log('部署请求数据:', deployData);

        // 调用部署接口
        const deployResult = await deployService.deployApp(deployData);

        console.log('部署结果:', deployResult);

        toast.success('应用部署请求已提交，正在创建实例...');

        // 构建最终配置，包含部署信息
        const finalConfig = {
          ...orderConfig,
          device,
          application: application,
          totalCost: priceInfo.totalCost,
          pricePerHour:
            priceInfo.gpuPrice + priceInfo.diskPrice + priceInfo.appCost,
          priceInfo,
          deploymentId: deployResult.id || deployResult.deploymentId,
          appConfig: appConfigValues,
          envVars: envVars,
        };

        await onConfirm(finalConfig);
      } else {
        // 不部署应用，只创建实例
        const finalConfig = {
          ...orderConfig,
          device,
          application: null,
          totalCost: priceInfo.totalCost,
          pricePerHour: priceInfo.gpuPrice + priceInfo.diskPrice,
          priceInfo,
        };

        await onConfirm(finalConfig);
      }
    } catch (error) {
      console.error('订购失败:', error);
      toast.error(error.message || '部署失败，请重试');
      setLoading(false);
    }
    // 注意：不在这里设置loading为false，因为需要保持loading状态直到跳转完成
  };

  const handleCancel = () => {
    form.resetFields();
    setCurrentStep(0);
    setOrderConfig({
      instanceName: '',
      pricingModel: 'ON_DEMAND',
      duration: 1,
      gpuCount: 1,
      extraDiskSize: 0,
      autoInstallApp: true,
      autoRenew: false,
      appConfig: {},
    });
    onCancel();
  };

  // 步骤1：确认配置
  const renderStep1 = () => (
    <div className="space-y-6">
      {/* 设备信息 */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <Server className="w-4 h-4" />
            <span>设备信息</span>
          </div>
        }
        size="small"
      >
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-gray-600">设备名称</div>
            <div className="font-medium">
              {device?.machineName || device?.name}
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">设备类型</div>
            <div className="font-medium">
              {device?.machineType || device?.type}
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">CPU</div>
            <div className="font-medium">
              {device?.cpuName || device?.cpu} ({device?.cpuNum}核)
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">内存</div>
            <div className="font-medium">
              {device?.memorySize || device?.memory}GB
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">GPU</div>
            <div className="font-medium">
              {device?.gpuName || device?.gpu} (最多{device?.gpuNum}卡)
            </div>
          </div>
          <div>
            <div className="text-sm text-gray-600">存储</div>
            <div className="font-medium">
              {device?.diskSize || device?.storage}GB {device?.diskType}
            </div>
          </div>
        </div>
      </Card>

      {/* 应用信息 */}
      {application && (
        <Card
          title={
            <div className="flex items-center space-x-2">
              <Package className="w-4 h-4" />
              <span>应用信息</span>
            </div>
          }
          size="small"
        >
          <div className="flex items-center space-x-4 mb-3">
            <div className="text-2xl">{application.icon}</div>
            <div>
              <div className="font-medium">{application.name}</div>
              <div className="text-sm text-gray-600">{application.version}</div>
            </div>
          </div>
          <div className="text-sm text-gray-700 mb-3">
            {application.description}
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">应用价格</span>
              <span className="font-medium text-green-600">
                {priceInfo.appPrice > 0
                  ? `${formatPrice(priceInfo.appPrice)}/小时`
                  : '免费'}
              </span>
            </div>

            <Form.Item
              name="autoInstallApp"
              valuePropName="checked"
              className="mb-0"
            >
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">
                  {priceInfo.appPrice > 0 ? '自动购买并安装' : '自动安装'}
                </span>
                <Switch
                  checked={orderConfig.autoInstallApp}
                  onChange={checked =>
                    setOrderConfig(prev => ({
                      ...prev,
                      autoInstallApp: checked,
                    }))
                  }
                />
              </div>
            </Form.Item>
          </div>
        </Card>
      )}

      {/* 实例配置 */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>实例配置</span>
          </div>
        }
        size="small"
      >
        <Form
          form={form}
          layout="horizontal"
          labelAlign="left"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <Form.Item
            name="instanceName"
            label="实例名称"
            rules={[{ required: true, message: '请输入实例名称' }]}
            initialValue={application ? `${application.name} 实例` : '我的实例'}
          >
            <Input
              placeholder="请输入实例名称"
              onChange={e =>
                setOrderConfig(prev => ({
                  ...prev,
                  instanceName: e.target.value,
                }))
              }
            />
          </Form.Item>

          <div className="grid grid-cols-1 gap-4">
            <Form.Item label="计费模式" className="mb-0">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                  <span className="text-sm font-medium text-blue-700">
                    按需计费（小时）
                  </span>
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  按实际使用时长计费，灵活便捷
                </div>
              </div>
            </Form.Item>

            <Form.Item
              name="duration"
              label="使用时长"
              rules={[{ required: true, message: '请输入使用时长' }]}
              initialValue={1}
              className="mb-0"
            >
              <div className="flex items-center space-x-4">
                <Slider
                  min={1}
                  max={168}
                  value={orderConfig.duration}
                  onChange={value =>
                    setOrderConfig(prev => ({
                      ...prev,
                      duration: value,
                    }))
                  }
                  className="flex-1"
                  marks={{
                    1: '1小时',
                    24: '1天',
                    168: '1周',
                  }}
                />
                <span className="text-sm text-gray-600 min-w-[80px]">
                  {orderConfig.duration} 小时
                </span>
              </div>
            </Form.Item>

            <Form.Item
              name="autoRenew"
              label="自动续费"
              initialValue={false}
              className="mb-0"
            >
              <Switch
                checked={orderConfig.autoRenew}
                onChange={checked =>
                  setOrderConfig(prev => ({
                    ...prev,
                    autoRenew: checked,
                  }))
                }
              />
            </Form.Item>
            <Form.Item label="GPU卡数" className="mb-0">
              <Select
                value={orderConfig.gpuCount}
                onChange={value =>
                  setOrderConfig(prev => ({ ...prev, gpuCount: value }))
                }
                className="w-full"
              >
                {Array.from({ length: device?.gpuNum || 1 }, (_, i) => (
                  <Option key={i + 1} value={i + 1}>
                    {i + 1}卡
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="额外磁盘" className="mb-0">
              <div className="flex items-center space-x-4">
                <Slider
                  min={0}
                  max={1000}
                  step={10}
                  value={orderConfig.extraDiskSize}
                  onChange={value =>
                    setOrderConfig(prev => ({
                      ...prev,
                      extraDiskSize: value,
                    }))
                  }
                  className="flex-1"
                  marks={{
                    0: '0GB',
                    1000: '1TB',
                  }}
                />
                <span className="text-sm text-gray-600 min-w-[80px]">
                  {orderConfig.extraDiskSize >= 1000
                    ? `${(orderConfig.extraDiskSize / 1000).toFixed(1)}TB`
                    : `${orderConfig.extraDiskSize}GB`}
                </span>
              </div>
              {orderConfig.extraDiskSize > 0 && (
                <div className="text-xs text-gray-500 mt-1">
                  额外费用: {formatPrice(priceInfo.diskPrice)}/小时
                </div>
              )}
            </Form.Item>
          </div>
        </Form>
      </Card>

      {/* 价格预览 */}
      <Card
        title={
          <div className="flex items-center space-x-2">
            <DollarSign className="w-4 h-4" />
            <span>价格预览</span>
          </div>
        }
        size="small"
      >
        <div className="space-y-3">
          <div className="flex justify-between">
            <span>GPU费用 ({orderConfig.gpuCount}卡)</span>
            <span>{formatPrice(priceInfo.gpuPrice)}/小时</span>
          </div>
          {orderConfig.extraDiskSize > 0 && (
            <div className="flex justify-between">
              <span>额外磁盘费用 ({orderConfig.extraDiskSize}GB)</span>
              <span>{formatPrice(priceInfo.diskPrice)}/小时</span>
            </div>
          )}
          {orderConfig.autoInstallApp && priceInfo.appCost > 0 && (
            <div className="flex justify-between">
              <span>应用费用</span>
              <span>{formatPrice(priceInfo.appCost)}/小时</span>
            </div>
          )}
          <Divider />
          <div className="flex justify-between">
            <span>小时单价合计</span>
            <span>{formatPrice(priceInfo.hourlyTotal)}/小时</span>
          </div>
          <div className="flex justify-between">
            <span>使用时长</span>
            <span>{orderConfig.duration} 小时</span>
          </div>
          <Divider />
          <div className="flex justify-between font-medium text-lg">
            <span>总计费用</span>
            <span className="text-blue-600">
              {formatPrice(priceInfo.totalCost)}
            </span>
          </div>
        </div>
      </Card>
    </div>
  );

  // 步骤2：应用配置
  const renderStep2 = () => (
    <div className="space-y-6">
      <Alert
        message="应用配置"
        description={`为 ${application?.name} 配置运行参数和环境变量`}
        type="info"
        showIcon
      />

      <Card title="基础配置" size="small">
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="appName"
                label="应用实例名称"
                rules={[
                  { required: true, message: '请输入应用实例名称' },
                  {
                    pattern: /^[a-z0-9-]+$/,
                    message: '应用名称只能包含小写字母、数字和连字符',
                  },
                ]}
                initialValue={
                  application?.name?.toLowerCase().replace(/[^a-z0-9]/g, '-') +
                  '-' +
                  Date.now().toString().slice(-4)
                }
              >
                <Input placeholder="my-app-1234" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="appPort"
                label="应用端口"
                rules={[{ required: true, message: '请输入应用端口' }]}
                initialValue={application?.protocolPortsList?.[0]?.port || 8080}
              >
                <Input type="number" placeholder="8080" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="appCommand"
            label="启动命令（可选）"
            help="如果为空，将使用应用模板的默认启动命令"
          >
            <Input placeholder={application?.startupCommand || 'npm start'} />
          </Form.Item>
        </Form>
      </Card>

      {/* 环境变量配置 */}
      <EnvironmentVariablesInput
        title="环境变量配置"
        size="small"
        showTitle={true}
      />

      {/* 应用模板预设的环境变量 */}
      {application?.environmentVariablesList &&
        application.environmentVariablesList.length > 0 && (
          <Card title="应用预设环境变量" size="small">
            <div className="space-y-2">
              {application.environmentVariablesList.map((envVar, index) => (
                <Row key={index} gutter={16}>
                  <Col span={10}>
                    <Input
                      value={envVar.key}
                      disabled
                      size="small"
                      addonBefore="变量名"
                    />
                  </Col>
                  <Col span={14}>
                    <Form.Item
                      name={['presetEnvVars', envVar.key]}
                      initialValue={envVar.value}
                      className="mb-0"
                    >
                      <Input
                        placeholder={envVar.value}
                        size="small"
                        addonBefore="变量值"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              ))}
            </div>
            <div className="mt-3 p-2 bg-yellow-50 rounded">
              <Text type="secondary" className="text-xs">
                💡 这些是应用模板预设的环境变量，您可以修改其值或保持默认值。
              </Text>
            </div>
          </Card>
        )}
    </div>
  );

  // 步骤3：订单确认
  const renderStep3 = () => (
    <div className="space-y-6">
      <Alert
        message="订单确认"
        description="请确认以下订单信息，确认后将开始创建实例"
        type="warning"
        showIcon
      />

      {/* 订单摘要 */}
      <Card title="订单摘要" size="small">
        <div className="space-y-3">
          <div className="flex justify-between">
            <span>实例名称</span>
            <span className="font-medium">{orderConfig.instanceName}</span>
          </div>
          <div className="flex justify-between">
            <span>设备</span>
            <span className="font-medium">{device?.name}</span>
          </div>
          <div className="flex justify-between">
            <span>计费模式</span>
            <span className="font-medium">按需计费（小时）</span>
          </div>
          <div className="flex justify-between">
            <span>使用时长</span>
            <span className="font-medium">{orderConfig.duration} 小时</span>
          </div>
          <div className="flex justify-between">
            <span>GPU卡数</span>
            <span className="font-medium">{orderConfig.gpuCount}卡</span>
          </div>
          {orderConfig.extraDiskSize > 0 && (
            <div className="flex justify-between">
              <span>额外磁盘</span>
              <span className="font-medium">{orderConfig.extraDiskSize}GB</span>
            </div>
          )}
          {orderConfig.autoInstallApp && application && (
            <div className="flex justify-between">
              <span>应用</span>
              <span className="font-medium">{application.name}</span>
            </div>
          )}
          <Divider className="my-2" />
          <div className="flex justify-between text-lg font-bold">
            <span>总费用</span>
            <span className="text-red-600">
              {formatPrice(priceInfo.totalCost)}
            </span>
          </div>
        </div>
      </Card>

      {/* 费用明细 */}
      <Card title="费用明细" size="small">
        <div className="space-y-3">
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-gray-700 mb-2">
              小时费用明细
            </div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>GPU费用 ({orderConfig.gpuCount}卡)</span>
                <span>
                  {formatPrice(priceInfo.gpuUnitPrice)} × {orderConfig.gpuCount}
                  卡 = {formatPrice(priceInfo.gpuPrice)}/小时
                </span>
              </div>
              {orderConfig.extraDiskSize > 0 && (
                <div className="flex justify-between">
                  <span>额外磁盘费用 ({orderConfig.extraDiskSize}GB)</span>
                  <span>
                    {formatPrice(priceInfo.diskUnitPrice)} ×{' '}
                    {orderConfig.extraDiskSize / 10}个10GB ={' '}
                    {formatPrice(priceInfo.diskPrice)}/小时
                  </span>
                </div>
              )}
              {orderConfig.autoInstallApp &&
                application &&
                priceInfo.appCost > 0 && (
                  <div className="flex justify-between">
                    <span>应用费用</span>
                    <span>{formatPrice(priceInfo.appCost)}/小时</span>
                  </div>
                )}
              <div className="border-t pt-2 flex justify-between font-medium">
                <span>小时单价合计</span>
                <span>{formatPrice(priceInfo.hourlyTotal)}/小时</span>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-blue-700 mb-2">
              总费用计算
            </div>
            <div className="text-sm text-blue-600">
              {formatPrice(priceInfo.hourlyTotal)}/小时 × {orderConfig.duration}
              小时 = {formatPrice(priceInfo.totalCost)}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      maskClosable={false}
      destroyOnClose={true}
    >
      <div className="space-y-6">
        {/* 步骤指示器 */}
        <Steps current={currentStep} size="small">
          {steps.map((step, index) => (
            <Steps.Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
            />
          ))}
        </Steps>

        {/* 步骤内容 */}
        <div className="min-h-[400px]">
          {currentStep === 0 && renderStep1()}
          {currentStep === 1 && hasAppConfig && renderStep2()}
          {currentStep === 1 && !hasAppConfig && renderStep3()}
          {currentStep === 2 && hasAppConfig && renderStep3()}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-between">
          <div>
            {currentStep > 0 && (
              <Button
                onClick={handlePrev}
                icon={<ArrowLeft className="w-4 h-4" />}
              >
                上一步
              </Button>
            )}
          </div>
          <div className="space-x-2">
            <Button onClick={handleCancel}>取消</Button>
            {/* 判断是否是最后一步 */}
            {(currentStep === 1 && !hasAppConfig) ||
            (currentStep === 2 && hasAppConfig) ? (
              <Button type="primary" loading={loading} onClick={handleConfirm}>
                {loading ? '实例创建中...' : '确认订购'}
              </Button>
            ) : (
              <Button
                type="primary"
                onClick={handleNext}
                icon={<ArrowRight className="w-4 h-4" />}
              >
                下一步
              </Button>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default InstanceOrderWizard;
